import React from 'react';
import { Navigate } from 'react-router-dom';
import { useContext } from 'react';
import { AuthContext } from '../context/AuthContext';

// Higher-order component to protect routes that require authentication
const withAuthProtection = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  const WithAuthProtection: React.FC<P> = (props) => {
    const auth = useContext(AuthContext);
    
    console.log(auth?.authToken, "-=-=")
    
    if (auth?.authToken) {
      return <WrappedComponent {...props} />;
    } else {
      // Redirect to login page if not authenticated
      return <Navigate to="/login" />;
    }
  };

  return WithAuthProtection;
};

export default withAuthProtection;