import axios from "axios";
import React, { createContext, useEffect, useState, type ReactNode } from "react";
import { getApiUrl } from "../utils/api";

type AuthContextType = {
  authToken: string | null;
  setAuthToken: (token: string | null) => void;
};

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
   const [authToken, setAuthToken] = useState<string | null>(null);
   const [loading, setLoading] = useState<boolean>(false);

  console.log("context provider rendered", new Date().toISOString());

  const checkAuth = async () => {
    try {
      setLoading(true)
      setAuthToken(localStorage.getItem("temp_token"))
      // const res = await axios.post(getApiUrl(`/profile/auth/token/refresh/`), {
      //   authToken,
      // });
      // console.log(res, "res from context");
    } catch (e) {
      console.log(e, "error from context");
    } finally {
      setLoading(false)
    }
  };

  console.log(authToken, "authToken");

  if(loading) return <h1>loading...</h1>

  useEffect(() => {
    checkAuth()
  }, []);

  return (
    <AuthContext.Provider value={{ authToken, setAuthToken }}>{children}</AuthContext.Provider>
  );
};
