import React, { useEffect, useState } from "react";
import DataTable from "../../components/common/dataTable";
import MobileMenu from "../../components/common/mobileMenu";
import InputField from "../../components/common/InputField";
import { MenuIcon, Search } from "lucide-react";
import withAuthProtection from "../../utils/withAuthProtection";
import axios from "axios";
import { getApiUrl } from "../../utils/api";

// Define the API response type for menu items
export interface MenuItemAPI {
  id: number;
  name: string;
  description: string;
  price: string | number;
  category: {
    id: string;
    name: string;
    slug?: string;
  };
  image?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

const DashboardPage: React.FC = () => {
  const [menuItems, setMenuItems] = useState<MenuItemAPI[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const getMenuItems = async () => {
    const token = localStorage.getItem("temp_token");

    try {
      setLoading(true);
      setError(null);

      const res = await axios.get(
        getApiUrl("/restaurant/menu-items/"),
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        },
      );

      console.log(res.data, "API response from getMenuItems");

      // Set the menu items directly from API
      setMenuItems(res.data);
    } catch (error) {
      console.error("Error fetching menu items:", error);
      setError("Failed to load menu items");

      // Fallback to empty array on error
      setMenuItems([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getMenuItems();
  }, []);

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange mx-auto mb-4"></div>
          <p className="text-gray-600">Loading menu items...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={getMenuItems}
            className="bg-orange text-white px-4 py-2 rounded-md hover:bg-orange/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* <MenuItems productData={productData}/> */}
      <div className="mb-2 flex items-center gap-3">
        <div className="grow">
          <InputField
            id="search"
            name="search"
            type="text"
            placeholder="Start Typing...."
            className=" bg-white border border-[#E6E6E6] rounded-lg"
            icon={<Search width={20} height={20} />}
            containerClassName="max-w-[700px]"
          />
        </div>
        <div className="flex items-center gap-4">
          <button className="bg-orange flex items-center gap-1 text-xs md:text-sm md:py-[10px] md:px-4 py-2 px-[6px] text-white cursor-pointer rounded-[10px]">
            <span>Preview Menu</span>
            <span>
              <MenuIcon width={16} height={16} />
            </span>
          </button>
          <select
            name="sorting"
            id="sorting"
            className="bg-white hidden md:block text-[#454952] text-sm py-[10px] px-4 rounded-[10px] border border-border"
          >
            <option value="highToLow">Rating : High To Low</option>
            <option value="highToLow">Rating : High To Low</option>
            <option value="highToLow">Rating : High To Low</option>
          </select>
        </div>
      </div>
      <div className="md:hidden">
        <MobileMenu menuItems={menuItems} />
      </div>

      <div className="hidden md:block">
        <DataTable menuItems={menuItems} />
      </div>
    </div>
  );
};

export default withAuthProtection(DashboardPage);
