import React, { useEffect, useState } from "react";
import DataTable from "../../components/common/dataTable";
import MobileMenu from "../../components/common/mobileMenu";
import InputField from "../../components/common/InputField";
import { MenuIcon, Search } from "lucide-react";
import withAuthProtection from "../../utils/withAuthProtection";
import axios from "axios";

// Define the product data type
export interface ProductData {
  id: number;
  photo: string;
  productName: string;
  price: number;
  foodType: string;
  qty: number;
  visibility: "active" | "inactive";
}

const DashboardPage: React.FC = () => {

  const getMenuItems = async () => {
    const token = localStorage.getItem("temp_token");

    try {
      const res = await axios.get(
        `${import.meta.env.VITE_API_BASE_URL}/restaurant/menu-items/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        },
      );
      console.log(res, "res from getMenuItems");
    } catch (error) {
      console.error("Error fetching menu items:", error);
    }
  };

  useEffect(() => {
    getMenuItems();
  }, []);

  // Centralized product data
  const [productData] = useState<ProductData[]>([
    {
      id: 1,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 12.99,
      foodType: "Fast Food",
      qty: 150,
      visibility: "active",
    },
    {
      id: 2,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 8.5,
      foodType: "Not Veg",
      qty: 75,
      visibility: "inactive",
    },
    {
      id: 3,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 15.99,
      foodType: "Veg",
      qty: 30,
      visibility: "active",
    },
    {
      id: 4,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 12.99,
      foodType: "Veg",
      qty: 150,
      visibility: "active",
    },
    {
      id: 5,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 8.5,
      foodType: "Not Veg",
      qty: 75,
      visibility: "inactive",
    },
    {
      id: 6,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 15.99,
      foodType: "Veg",
      qty: 30,
      visibility: "active",
    },
    {
      id: 7,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 12.99,
      foodType: "Veg",
      qty: 150,
      visibility: "active",
    },
    {
      id: 8,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 12.99,
      foodType: "Veg",
      qty: 150,
      visibility: "active",
    },
    {
      id: 9,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 12.99,
      foodType: "Veg",
      qty: 150,
      visibility: "active",
    },
    {
      id: 10,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 12.99,
      foodType: "Veg",
      qty: 150,
      visibility: "active",
    },
    {
      id: 11,
      photo: "/food.png",
      productName: "Italian Vegetable Cheese Pizza",
      price: 12.99,
      foodType: "Veg",
      qty: 150,
      visibility: "active",
    },
  ]);

  return (
    <div>
      {/* <MenuItems productData={productData}/> */}
      <div className="mb-2 flex items-center gap-3">
        <div className="grow">
          <InputField
            id="search"
            name="search"
            type="text"
            placeholder="Start Typing...."
            className=" bg-white border border-[#E6E6E6] rounded-lg"
            icon={<Search width={20} height={20} />}
            containerClassName="max-w-[700px]"
          />
        </div>
        <div className="flex items-center gap-4">
          <button className="bg-orange flex items-center gap-1 text-xs md:text-sm md:py-[10px] md:px-4 py-2 px-[6px] text-white cursor-pointer rounded-[10px]">
            <span>Preview Menu</span>
            <span>
              <MenuIcon width={16} height={16} />
            </span>
          </button>
          <select
            name="sorting"
            id="sorting"
            className="bg-white hidden md:block text-[#454952] text-sm py-[10px] px-4 rounded-[10px] border border-border"
          >
            <option value="highToLow">Rating : High To Low</option>
            <option value="highToLow">Rating : High To Low</option>
            <option value="highToLow">Rating : High To Low</option>
          </select>
        </div>
      </div>
      <div className="md:hidden">
        <MobileMenu productData={productData} />
      </div>

      <div className="hidden md:block">
        <DataTable productData={productData} />
      </div>
    </div>
  );
};

export default withAuthProtection(DashboardPage);
