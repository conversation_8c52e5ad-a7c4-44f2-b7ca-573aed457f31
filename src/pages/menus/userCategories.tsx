import { useApi } from "../../context/ApiContext";
import { Link } from "react-router-dom";
import { getApiUrl } from "../../utils/api";
import axios from "axios";
import { useEffect, useState } from "react";
import Loader from "../../components/common/loader";

interface Outlet {
  name: string;
  slug: string;
}

interface Category {
  name: string;
  slug: string;
  image: string;
}

const UserCategories = () => {
  const { userSlug } = useApi() as { userSlug: Outlet | null }; // Type the context return

  const [userCategories, setUserCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchUserCategories = async () => {
    try {
      setLoading(true);
      const res = await axios.get(
        getApiUrl(`/api/open/category/${userSlug?.slug}`)
      );
      setUserCategories(res?.data?.data?.categories || []);
      console.log({ res });
    } catch (e) {
      console.log({ e });
    } finally {
      setLoading(false);
    }
  };

  const handleImageError = (
    e: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    e.currentTarget.src = "/food.png"; // fallback image
  };

  useEffect(() => {
    if (userSlug?.slug) {
      fetchUserCategories();
    }
  }, [userSlug?.slug]);

  if (loading) return <Loader />;

  return (
    <section>
      <h1 className="my-4 text-base font-semibold tracking-[1px] text-[#111719] uppercase">
        Whatever You Need
      </h1>

      <ul className="grid grid-cols-3 gap-3 sm:grid-cols-3 md:grid-cols-4">
        {userCategories.map((item) => (
          <li key={item.slug} className="flex flex-col overflow-hidden">
            <Link
              to={`${item.slug}`}
              className="flex flex-col h-full hover:opacity-90 transition-opacity"
            >
              <div className="relative h-[120px] w-full">
                <img
                  src={`https://dev.dishto.in${item.image}`}
                  alt={item.name}
                  className="h-full w-full object-cover rounded-xl"
                  onError={handleImageError}
                />
              </div>
              <div className="flex flex-col gap-[6px] p-[10px]">
                <span className="text-xs font-semibold text-[#111719] line-clamp-1">
                  {item.name}
                </span>
              </div>
            </Link>
          </li>
        ))}
      </ul>
    </section>
  );
};

export default UserCategories;
