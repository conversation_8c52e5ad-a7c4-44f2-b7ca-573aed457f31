import axios from "axios";
import React from "react";
import { useForm } from "react-hook-form";
import type { SubmitHandler } from "react-hook-form";
import { getApiUrl } from "../../utils/api";

interface LoginFormData {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const { register, handleSubmit } = useForm<LoginFormData>();

  const handleLogin: SubmitHandler<LoginFormData> = async (data) => {
    try {
      const res = await axios.post(getApiUrl(`/profile/auth/token/`), data);
      localStorage.setItem("temp_token", res?.data?.access);
      console.log(res, "res");
    } catch (e) {
      console.log(e, "error");
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit(handleLogin)}>
        <input type="text" placeholder="email" {...register("email")} />
        <input
          type="password"
          placeholder="password"
          {...register("password")}
        />
        <button type="submit">Login</button>
      </form>
    </div>
  );
};

export default Login;
