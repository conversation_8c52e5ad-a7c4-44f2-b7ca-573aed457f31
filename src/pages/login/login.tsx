import { useForm } from "react-hook-form";
import type { SubmitHandler } from "react-hook-form";
import axios from "axios";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import InputField from "../../components/common/InputField";
import CustomButton from "../../components/common/CustomButton";
import Icon from "../../components/common/Icon";
import { getApiUrl } from "../../utils/api";
import { useAuth } from "../../context/AuthContext";

// Interface for login form data
interface LoginFormData {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const navigate = useNavigate();

  const { user, setUser, loading, setLoading, isAuth, setIsAuth } = useAuth();
  // console.log({ user });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>();

  // Handle form submission
  const handleLogin: SubmitHandler<LoginFormData> = async (data) => {
    try {
      setLoading(true);

      const res = await axios.post(
        getApiUrl("/api/protected/auth/login"),
        data,
        {
          withCredentials: true,
        }
      );

      if (res?.data?.access) {
        // Token is now handled by cookies, no need to manually set
        toast.success("Login successful!");

        // Redirect to menu list page after successful login
        navigate("/");
        setUser(true);
        setIsAuth(true);
      } else {
        toast.error("Invalid response from server");
      }
    } catch (error: any) {
      console.error("Login error:", error);

      // Handle different error scenarios
      if (error.response?.status === 401) {
        toast.error("Invalid email or password");
      } else if (error.response?.status >= 500) {
        toast.error("Server error. Please try again later.");
      } else {
        toast.error("Login failed. Please check your credentials.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <section>
      <div>
        <div className="max-w-[150px]">
          <img src="/login-logo.png" alt="Logo" />
        </div>
      </div>
      <div className="flex">
        <div className="flex max-w-2/5 w-full shrink-0 items-center justify-center -mt-[100px] min-h-screen overflow-y-hidden -m-10">
          <div className="max-w-[450px] w-full">
            <div className="max-w-xl w-full">
              <div className="text-center mb-6">
                <h2 className="text-2xl md:text-[40px] font-bold text-[#07143B] font-playfair mb-2">
                  Sign In
                </h2>
                <p className="text-[#959895]">Sign in to stay connected.</p>
              </div>

              <div>
                <form
                  onSubmit={handleSubmit(handleLogin)}
                  className="space-y-6"
                >
                  {/* Email Field */}
                  <div>
                    <InputField
                      id="email"
                      type="email"
                      label="Email *"
                      labelClassName="text-[#959895]"
                      placeholder="Enter your email"
                      className="bg-white border border-[var(--color-orange)] !rounded-3xl h-11"
                      iconPosition="left"
                      {...register("email", {
                        required: "Email is required",
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: "Invalid email address",
                        },
                      })}
                      error={errors.email?.message}
                    />
                  </div>

                  {/* Password Field */}
                  <div>
                    <InputField
                      id="password"
                      type="password"
                      labelClassName="text-[#959895]"
                      label="Password *"
                      placeholder="Enter your password"
                      className="bg-white border border-[var(--color-orange)] !rounded-3xl h-11"
                      iconPosition="left"
                      {...register("password", {
                        required: "Password is required",
                        minLength: {
                          value: 6,
                          message:
                            "Password must be at least 6 characters long",
                        },
                      })}
                      error={errors.password?.message}
                    />
                  </div>

                  {/* Submit Button */}
                  <div>
                    <CustomButton
                      label={loading ? "Signing in..." : "Sign In"}
                      bgColor="bg-[var(--color-orange)]"
                      textColor="text-white"
                      className="max-w-[200px] h-11 !rounded-3xl mx-auto w-full py-3 text-base font-medium"
                      disabled={loading}
                    />
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div className="max-w-3/5 w-full absolute right-0 top-0 h-screen overflow-hidden">
          <div className="image-grid-container transform rotate-12 scale-110 origin-center h-full w-full">
            <div className="grid grid-cols-3 gap-4 h-full">
              {/* Column 1 */}
              <div className="flex flex-col gap-4 animate-scroll-up">
                <img
                  src="https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400&h=600&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=400&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=400&h=700&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=500&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                
                {/* Duplicate for seamless loop */}
                <img
                  src="https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400&h=600&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=400&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=400&h=700&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=500&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
              </div>

              {/* Column 2 */}
              <div className="flex flex-col gap-4 animate-scroll-down">
                <img
                  src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=500&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400&h=650&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=400&h=400&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1574071318508-1cdbab80d002?w=400&h=600&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                
                {/* Duplicate for seamless loop */}
                <img
                  src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400&h=500&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400&h=650&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=400&h=400&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1574071318508-1cdbab80d002?w=400&h=600&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
              </div>

              {/* Column 3 */}
              <div className="flex flex-col gap-4 animate-scroll-up">
                <img
                  src="https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=400&h=400&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1565958011703-44f9829ba187?w=400&h=700&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1565299585323-38174c4a6c7b?w=400&h=500&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=600&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                
                {/* Duplicate for seamless loop */}
                <img
                  src="https://images.unsplash.com/photo-1565299507177-b0ac66763828?w=400&h=400&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1565958011703-44f9829ba187?w=400&h=700&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1565299585323-38174c4a6c7b?w=400&h=500&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
                <img
                  src="https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=600&fit=crop"
                  alt=""
                  className="rounded-lg object-cover w-full "
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Login;
