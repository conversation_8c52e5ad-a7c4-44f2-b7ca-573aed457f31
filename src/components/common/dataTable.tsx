import { AgGridReact } from "ag-grid-react";
import React from "react";
import type {
  ColDef,
  ICellRendererParams,
  ValueFormatterParams,
} from "ag-grid-community";
import type { ProductData } from "../../pages/dashboard/dashboardPage";
import { Ellip<PERSON>, Eye, EyeOff } from "lucide-react";
import CustomButton from "./CustomButton";

// Create type-safe cell renderer params
interface PhotoCellRendererParams extends ICellRendererParams {
  value: string;
}

interface TextCellRendererParams extends ICellRendererParams {
  value: string;
}

interface PriceCellRendererParams extends ICellRendererParams {
  value: number;
}

interface FoodTypeCellRendererParams extends ICellRendererParams {
  value: string;
}

interface VisibilityCellRendererParams extends ICellRendererParams {
  value: "active" | "inactive";
}

interface DataTableProps {
  productData: ProductData[];
}

const DataTable: React.FC<DataTableProps> = ({ productData }) => {
  const colDefs: ColDef<ProductData>[] = [
    {
      field: "photo",
      headerName: "Photo",
      cellRenderer: (params: PhotoCellRendererParams) => {
        return (
          <div className="flex justify-center max-w-[80px]">
            <img
              src={params.value}
              alt="Product"
              className="rounded-md object-cover"
            />
          </div>
        );
      },
      width: 100,
    },
    {
      field: "productName",
      headerName: "Product Name",
      cellRenderer: (params: TextCellRendererParams) => {
        return <span className="font-semibold">{params.value}</span>;
      },
      width: 200,
    },
    {
      field: "price",
      headerName: "Price",
      cellRenderer: (params: PriceCellRendererParams) => {
        return (
          <span className="text-orange-600 font-medium">
            ₹{params.value.toFixed(2)}
          </span>
        );
      },
      width: 100,
    },
    {
      field: "foodType",
      headerName: "Food Type",
      cellRenderer: (params: FoodTypeCellRendererParams) => {
        return (
          <div
            className={`flex items-center py-[2px] px-[10px] ${
              params.value === "Veg"
                ? "text-[var(--color-green)] border border-[var(--color-green)] rounded-2xl"
                : "text-red-500 border border-red-500 rounded-2xl"
            }`}
          >
            <span
              className={`w-2 h-2 rounded-full mr-2 ${
                params.value === "Veg"
                  ? "bg-[var(--color-green)]"
                  : "bg-red-500"
              }`}
            ></span>
            <span className="text-sm">{params.value}</span>
          </div>
        );
      },
      width: 120,
    },
    {
      field: "qty",
      headerName: "Quantity",
      valueFormatter: (params: ValueFormatterParams<ProductData, number>) =>
        `Qty: ${params.value}`,
      width: 120,
    },
    {
      field: "visibility",
      headerName: "Visibility",
      cellRenderer: (params: VisibilityCellRendererParams) => {
        return (
          <div className="flex justify-center">
            {params.value === "active" ? (
              <CustomButton
                label="Active"
                icon={<Eye height={16} width={16} />}
                bgColor="bg-[var(--color-green-light)]"
                className="border border-active !px-3 !py-[6px]"
                textColor="text-[var(--color-active)]"
              />
            ) : (
              <CustomButton
                label="Inactive"
                icon={<EyeOff height={16} width={16} />}
                bgColor="bg-red-light"
                className="border border-red !px-3 !py-[6px]"
                textColor="text-red"
              />
            )}
          </div>
        );
      },
      width: 120,
    },
    {
      headerName: "Action",
      cellRenderer: () => {
        return (
          <div className="flex justify-center">
            <button className="p-1">
              <Ellipsis />
            </button>
          </div>
        );
      },
      width: 100,
    },
  ];

  return (
    <div className="w-full h-screen">
      <div className="custom-ag-table w-full h-full">
        <AgGridReact<ProductData>
          rowData={productData}
          columnDefs={colDefs}
          getRowClass={() => "custom-row"}
          defaultColDef={{
            resizable: true,
            sortable: true,
            flex: 1,
          }}
          domLayout="normal"
          rowHeight={120}
          headerHeight={50}
          className="rounded-md"
        />
      </div>
    </div>
  );
};

export default DataTable;
