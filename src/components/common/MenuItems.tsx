import React from "react";
import type { ProductData } from "../../pages/dashboard/dashboardPage";

interface MenuItemsProps {
  productData: ProductData[];
}

const MenuItems: React.FC<MenuItemsProps> = ({ productData }) => {
  const SliderItems = productData.map((item) => {
    return (
      <li key={item?.id} className="bg-white border border-[#E6E6E6] rounded-[12px] p-4">
        <div className="flex items-center gap-4">
          <div>
            <img src={item?.photo} alt="item" />
          </div>
          <div className="flex flex-col">
            <span className="text-base font-semibold mb-1 line-clamp-1">{item?.productName}</span>
            <span className="text-[#00000099] text-s">23 Items Available</span>
          </div>
        </div>
      </li>
    );
  });
  return (
    <div className="mb-5">
      <button className="flex justify-end w-full mb-4 cursor-pointer">View All</button>
      <ul className="flex gap-5">
        {SliderItems}
      </ul>
    </div>
  );
};

export default MenuItems;
