import React from "react";
import type { MenuItemAPI } from "../../pages/dashboard/dashboardPage";

interface MenuItemsProps {
  menuItems: MenuItemAPI[];
}

const MenuItems: React.FC<MenuItemsProps> = ({ menuItems }) => {
  const SliderItems = menuItems.map((item) => {
    return (
      <li
        key={item?.id}
        className="bg-white border border-[#E6E6E6] rounded-[12px] p-4 min-w-[280px] flex-shrink-0"
      >
        <div className="flex items-center gap-4">
          <div className="shrink-0">
            <img
              src={item?.image || "/food.png"}
              alt="item"
              className="w-12 h-12 object-cover rounded-md"
            />
          </div>
          <div className="flex flex-col min-w-0 flex-1">
            <span className="text-base font-semibold mb-1 line-clamp-1">
              {item?.name}
            </span>
            <span className="text-[#00000099] text-sm">
              {item?.category?.name || "Unknown Category"}
            </span>
          </div>
        </div>
      </li>
    );
  });
  return (
    <div className="mb-5">
      <div>
        <button className="flex justify-end w-full mb-4 cursor-pointer">
          View All
        </button>
      </div>
      <ul className="flex gap-5 w-[100vw] overflow-x-scroll">{SliderItems}</ul>
    </div>
  );
};

export default MenuItems;
