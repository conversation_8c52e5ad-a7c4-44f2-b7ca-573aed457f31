import React, { useState, memo } from "react";
import type { ProductData } from "../../pages/dashboard/dashboardPage";
import { EllipsisVertical, Eye, EyeOff } from "lucide-react";
import CustomButton from "./CustomButton";

interface MobileMenuProps {
  productData: ProductData[];
}

const MobileMenu: React.FC<MobileMenuProps> = ({ productData }) => {
  console.log("MobileMenu rendering", new Date().toISOString());
  const [activeButton, setActiveButton] = useState<number | null>(null);

  const toggleButton = (buttonIndex: number) => {
    if (activeButton === buttonIndex) {
      setActiveButton(null);
    } else {
      setActiveButton(buttonIndex);
    }
  };

  return (
    <ul className="flex flex-col space-y-2">
      {productData.map((product) => (
        <li
          key={product.id}
          className={`w-full text-left p-[10px] rounded-md ${
            activeButton === product.id
              ? "bg-orange-light border-orange-border border"
              : "bg-white border border-[#E6E6E6]"
          }`}
        >
          <div onClick={() => toggleButton(product.id)}>
            <div className="flex items-start">
              <div>
                <EllipsisVertical height={20} width={20} />
              </div>
              <div className="flex items-center max-w-11">
                <img
                  src={product.photo}
                  alt={product.productName}
                  className="rounded-md object-cover"
                />
              </div>
              <div className="flex flex-col mx-[10px] text-left">
                <span className="font-semibold text-sm lg:text-base">
                  {product.productName}
                </span>
                <span className="text-orange font-semibold text-sm lg:text-base">
                  ₹{product.price.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                {product.visibility === "active" ? (
                  <CustomButton
                    label="Active"
                    icon={<Eye height={16} width={16} />}
                    bgColor="bg-[var(--color-green-light)]"
                    className="border border-active !px-3 !py-[6px]"
                    textColor="text-[var(--color-active)]"
                  />
                ) : (
                  <CustomButton
                    label="Inactive"
                    icon={<EyeOff height={16} width={16} />}
                    bgColor="bg-red-light"
                    className="border border-red !px-3 !py-[6px]"
                    textColor="text-red"
                  />
                )}
              </div>
            </div>
          </div>

          {activeButton === product.id && (
            <div className="text-sm mt-4">
              <ul>
                <li className="flex mb-2">
                  <span className="font-medium basis-[30%]">Food Type:</span>
                  <span>{product.foodType}</span>
                </li>
                <li className="flex  mb-2">
                  <span className="font-medium basis-[30%]">Quantity:</span>
                  <span>{product.qty}</span>
                </li>
              </ul>
            </div>
          )}
        </li>
      ))}
    </ul>
  );
};

export default memo(MobileMenu);
