import React, { useState } from "react";
import { ChevronRight, Clipboard, LayoutGrid, SquareCheck } from "lucide-react";
import { Link } from "react-router-dom";
import { PAGE_CATEGORIES, PAGE_CATEGORIES_ADD, PAGE_CATEGORIES_LIST, PAGE_DASHBOARD, PAGE_ORDER } from "../../constants/page";
import Drawer from "react-modern-drawer";

//import styles 👇
import "react-modern-drawer/dist/index.css";

interface SideBarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

interface MenuItemProps {
  icon: React.ReactNode;
  text: string;
  path: string;
  isOpen: boolean;
}

const MenuItem: React.FC<MenuItemProps> = ({ icon, text, path, isOpen }) => {
  return (
    <li className="py-[10px] px-5 hover:bg-orange-light hover:rounded-[11px] hover:text-orange transition-all duration-300 ease-in-out">
      <Link to={path} className="flex items-center">
        {icon}
        <span
          className={`text-base font-medium ml-4 flex-grow transition-opacity duration-300 ${isOpen ? "opacity-100" : "opacity-0 w-0 overflow-hidden"}`}
        >
          {text}
        </span>
        <span
          className={`grow justify-end flex transition-opacity duration-300 ${isOpen ? "opacity-100" : "opacity-0 w-0 overflow-hidden"}`}
        >
          <ChevronRight />
        </span>
      </Link>
    </li>
  );
};

interface SubMenuItemProps {
  text: string;
  path: string;
}

const SubMenuItem: React.FC<SubMenuItemProps> = ({ text, path }) => {
  return (
    <li className="py-2 text-sm text-black/60 hover:text-orange flex gap-2 items-center transition-colors duration-300">
      <span>
        <svg
          width="20"
          height="9"
          viewBox="0 0 20 9"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M1 4.5H5" stroke="currentColor" strokeLinecap="round" />
          <circle cx="10" cy="4.5" r="3.5" stroke="currentColor" />
          <path d="M15 4.5H19" stroke="currentColor" strokeLinecap="round" />
        </svg>
      </span>
      <Link to={path}>{text}</Link>
    </li>
  );
};

const SideBar: React.FC<SideBarProps> = ({ isOpen, toggleSidebar }) => {
  const [isMenusOpen, setIsMenusOpen] = useState<boolean>(false);

  const toggleMenus = (): void => setIsMenusOpen(!isMenusOpen);

  return (
    // <aside
    //   className={`bg-white border-r border-[#E7E7E7] p-5 transition-all duration-300 ease-in-out fixed h-full z-10
    //     ${isOpen ? "max-w-[350px] w-[350px] opacity-100 translate-x-0" : "max-w-0 w-0 opacity-0 -translate-x-full overflow-hidden"}`}
    // >
    //   <h1 className="text-[48px] font-bold">Order It.</h1>
    //   <ul className="flex flex-col gap-4 mt-10">
    //     {/* Dashboard */}
    //     <MenuItem
    //       icon={<LayoutGrid />}
    //       text="Dashboard"
    //       path={PAGE_DASHBOARD.path}
    //       isOpen={true}
    //     />

    //     {/* Order */}
    //     <MenuItem
    //       icon={<SquareCheck />}
    //       text="Order"
    //       path={PAGE_ORDER.path}
    //       isOpen={true}
    //     />

    //     {/* Menus with submenu */}
    //     <div>
    //       <li
    //         onClick={toggleMenus}
    //         className={`py-[10px] px-5 cursor-pointer transition-all duration-300 ease-in-out ${
    //           isMenusOpen
    //             ? "bg-orange-light text-orange rounded-[11px]"
    //             : "hover:bg-orange-light hover:text-orange hover:rounded-[11px]"
    //         }`}
    //       >
    //         <div className="flex items-center justify-between">
    //           <div className="flex items-center">
    //             <Clipboard />
    //             <span className="text-base font-medium ml-4">Menus</span>
    //           </div>
    //           <ChevronRight
    //             className={`transition-transform duration-300 ${
    //               isMenusOpen ? "rotate-90" : ""
    //             }`}
    //           />
    //         </div>
    //       </li>

    //       {/* Submenu */}
    //       <ul
    //         className={`ml-12 mt-2 overflow-hidden transition-all duration-300 ease-in-out ${
    //           isMenusOpen ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
    //         }`}
    //       >
    //         <SubMenuItem text="Add Menu" path="/" />
    //         <SubMenuItem text="Manage Menus" path="/" />
    //       </ul>
    //     </div>
    //   </ul>
    // </aside>
    <Drawer
      open={isOpen}
      onClose={toggleSidebar}
      direction="left"
      className="border-r border-border p-5"
      size={350}
    >
      <div>
        <h1 className="text-[48px] font-bold">Order It.</h1>
        <ul className="flex flex-col gap-4 mt-10">
          {/* Dashboard */}
          <MenuItem
            icon={<LayoutGrid />}
            text="Dashboard"
            path={PAGE_DASHBOARD.path}
            isOpen={true}
          />

          {/* <MenuItem
            icon={<LayoutGrid />}
            text="Categories"
            path={PAGE_CATEGORIES.path}
            isOpen={true}
          /> */}

          {/* Order */}
          <MenuItem
            icon={<SquareCheck />}
            text="Order"
            path={PAGE_ORDER.path}
            isOpen={true}
          />

          {/* Menus with submenu */}
          <div>
            <li
              onClick={toggleMenus}
              className={`py-[10px] px-5 cursor-pointer transition-all duration-300 ease-in-out ${isMenusOpen
                  ? "bg-orange-light text-orange rounded-[11px]"
                  : "hover:bg-orange-light hover:text-orange hover:rounded-[11px]"
                }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Clipboard />
                  <span className="text-base font-medium ml-4">Categories</span>
                </div>
                <ChevronRight
                  className={`transition-transform duration-300 ${isMenusOpen ? "rotate-90" : ""
                    }`}
                />
              </div>
            </li>

            {/* Submenu */}
            <ul
              className={`ml-12 mt-2 overflow-hidden transition-all duration-300 ease-in-out ${isMenusOpen ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
                }`}
            >
              <SubMenuItem text="Add Categories" path={PAGE_CATEGORIES_ADD.path} />
              <SubMenuItem text="Categories List" path={PAGE_CATEGORIES_LIST.path} />
            </ul>
          </div>
        </ul>
      </div>
    </Drawer>
  );
};

export default SideBar;
