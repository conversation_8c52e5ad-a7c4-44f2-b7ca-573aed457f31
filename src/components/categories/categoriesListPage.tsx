import axios from "axios";
import React, { useEffect, useState } from "react";

interface Category {
  id: string;
  name: string;
  description: string;
  outlet: string;
}

const CategoriesListPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const getCategories = async () => {
    const token = localStorage.getItem("temp_token");

    try {
      const res = await axios.get(
        `${import.meta.env.VITE_API_BASE_URL}/restaurant/categories/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      setCategories(res.data)
      console.log(res, "res from categoriesListPage");
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  useEffect(() => {
    getCategories();
  }, []);

  return (
    <div>
      <div>CategoriesListPage</div>
      <div>{JSON.stringify(categories)}</div>
    </div>
  );
};

export default CategoriesListPage;
