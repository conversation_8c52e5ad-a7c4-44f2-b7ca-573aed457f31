import React, { useEffect, useState } from "react";
import axios from "axios";
import { Search, Plus, Edit, Trash2, Eye, EyeOff } from "lucide-react";
import withAuthProtection from "../../utils/withAuthProtection";
import { getApiUrl } from "../../utils/api";
import InputField from "../common/InputField";
import CustomButton from "../common/CustomButton";
import toast from "react-hot-toast";

// Interface for category data from API
interface Category {
  id: string;
  name: string;
  description: string;
  outlet: string;
  slug?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

const CategoriesListPage: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");

  // Filter categories based on search term
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getCategories = async () => {
    const token = localStorage.getItem("temp_token");

    try {
      setLoading(true);
      setError(null);

      const res = await axios.get(
        getApiUrl("/restaurant/categories/"),
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        },
      );

      setCategories(res.data);
      console.log(res.data, "Categories from API");
    } catch (error) {
      console.error("Error fetching categories:", error);
      setError("Failed to load categories");
      toast.error("Failed to load categories");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (!window.confirm("Are you sure you want to delete this category?")) {
      return;
    }

    const token = localStorage.getItem("temp_token");

    try {
      await axios.delete(
        getApiUrl(`/restaurant/categories/${categoryId}/`),
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      toast.success("Category deleted successfully!");
      getCategories(); // Refresh the list
    } catch (error) {
      console.error("Error deleting category:", error);
      toast.error("Failed to delete category");
    }
  };

  useEffect(() => {
    getCategories();
  }, []);

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange mx-auto mb-4"></div>
          <p className="text-gray-600">Loading categories...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={getCategories}
            className="bg-orange text-white px-4 py-2 rounded-md hover:bg-orange/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Categories</h1>
        <p className="text-gray-600">Manage your restaurant categories</p>
      </div>

      <div className="mb-6 flex items-center gap-3">
        <div className="grow">
          <InputField
            id="search"
            name="search"
            type="text"
            placeholder="Search categories..."
            className="bg-white border border-[#E6E6E6] rounded-lg"
            icon={<Search width={20} height={20} />}
            containerClassName="max-w-[500px]"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-4">
          <CustomButton
            label="Add Category"
            icon={<Plus width={16} height={16} />}
            bgColor="bg-[var(--color-orange)]"
            textColor="text-white"
            className="px-4 py-2"
            onClick={() => window.location.href = '/categories/add'}
          />
        </div>
      </div>

      {/* Desktop Table View */}
      <div className="hidden md:block bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-green text-white">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-semibold">Name</th>
                <th className="px-6 py-4 text-left text-sm font-semibold">Description</th>
                <th className="px-6 py-4 text-left text-sm font-semibold">Status</th>
                <th className="px-6 py-4 text-left text-sm font-semibold">Created</th>
                <th className="px-6 py-4 text-center text-sm font-semibold">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredCategories.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                    {searchTerm ? "No categories found matching your search." : "No categories found."}
                  </td>
                </tr>
              ) : (
                filteredCategories.map((category) => (
                  <tr key={category.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="font-semibold text-gray-900">{category.name}</div>
                      {category.slug && (
                        <div className="text-sm text-gray-500">Slug: {category.slug}</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-gray-700 max-w-xs truncate">
                        {category.description || "No description"}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex justify-start">
                        {category.is_active !== false ? (
                          <CustomButton
                            label="Active"
                            icon={<Eye height={16} width={16} />}
                            bgColor="bg-[var(--color-green-light)]"
                            className="border border-active !px-3 !py-[6px]"
                            textColor="text-[var(--color-active)]"
                          />
                        ) : (
                          <CustomButton
                            label="Inactive"
                            icon={<EyeOff height={16} width={16} />}
                            bgColor="bg-red-light"
                            className="border border-red !px-3 !py-[6px]"
                            textColor="text-red"
                          />
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {category.created_at
                        ? new Date(category.created_at).toLocaleDateString()
                        : "N/A"
                      }
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex justify-center space-x-2">
                        <button
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                          title="Edit Category"
                        >
                          <Edit width={16} height={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteCategory(category.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                          title="Delete Category"
                        >
                          <Trash2 width={16} height={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-4">
        {filteredCategories.length === 0 ? (
          <div className="bg-white rounded-lg p-6 text-center text-gray-500">
            {searchTerm ? "No categories found matching your search." : "No categories found."}
          </div>
        ) : (
          filteredCategories.map((category) => (
            <div key={category.id} className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 mb-1">{category.name}</h3>
                  {category.slug && (
                    <p className="text-sm text-gray-500 mb-2">Slug: {category.slug}</p>
                  )}
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {category.description || "No description"}
                  </p>
                </div>
                <div className="ml-4">
                  {category.is_active !== false ? (
                    <CustomButton
                      label="Active"
                      icon={<Eye height={14} width={14} />}
                      bgColor="bg-[var(--color-green-light)]"
                      className="border border-active !px-2 !py-1 !text-xs"
                      textColor="text-[var(--color-active)]"
                    />
                  ) : (
                    <CustomButton
                      label="Inactive"
                      icon={<EyeOff height={14} width={14} />}
                      bgColor="bg-red-light"
                      className="border border-red !px-2 !py-1 !text-xs"
                      textColor="text-red"
                    />
                  )}
                </div>
              </div>

              <div className="flex justify-between items-center pt-3 border-t border-gray-100">
                <div className="text-xs text-gray-500">
                  Created: {category.created_at
                    ? new Date(category.created_at).toLocaleDateString()
                    : "N/A"
                  }
                </div>
                <div className="flex space-x-2">
                  <button
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                    title="Edit Category"
                  >
                    <Edit width={16} height={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteCategory(category.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    title="Delete Category"
                  >
                    <Trash2 width={16} height={16} />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      <div className="mt-6 text-center text-sm text-gray-500">
        Showing {filteredCategories.length} of {categories.length} categories
      </div>
    </div>
  );
};

export default withAuthProtection(CategoriesListPage);
