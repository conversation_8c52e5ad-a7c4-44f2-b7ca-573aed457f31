import axios from "axios";
import React from "react";
import { useForm } from "react-hook-form";
import type { SubmitHandler } from "react-hook-form";
import toast from "react-hot-toast";
import { Link } from "react-router-dom";

interface CategoryFormData {
  name: string;
  outlet: string;
  description: string;
}

const CategoriesForm: React.FC = () => {
  const { register, handleSubmit } = useForm<CategoryFormData>();
  const token = localStorage.getItem('temp_token');

  const handleAddCategory: SubmitHandler<CategoryFormData> = async (data) => {
    // Add your code here to save the category to the database
    console.log(data);

    try {
      const res = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/restaurant/categories/`, data, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      toast.success("Category added successfully!");
      console.log(res)
    }
    catch (error) {
      console.error("Error adding category:", error);
    }
  }

  return (
    <div>
      <form onSubmit={handleSubmit(handleAddCategory)}>
        <div>
          <input type="text" placeholder="Enter Category Name"  {...register("name")} />
          <input type="hidden" value={'2b9971d41f2b4b69_1749317404438'} {...register("outlet")} />
          <input type="text" placeholder="Enter Category description" {...register("description")} />
          <button>Submit</button>
        </div>
      </form>
      <Link to={'/categories/list'}>View Categories</Link>
    </div>
  )
}

export default CategoriesForm