import IconButton from "../common/IconButton";
import Icon from "../common/Icon";
import { useAuth } from "../../context/AuthContext";
import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import axios from "axios";
import { getApiUrl } from "../../utils/api";
import toast from "react-hot-toast";
import Loader from "../common/loader";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

interface HeaderProps {
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar }) => {
  const [toggle, setToggle] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profileLoading, setProfileLoading] = useState(true); // Add a separate loading state
  const { userInfo } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      setLoading(true);
      const res = await axios.post(getApiUrl(`/api/protected/auth/logout`));
      console.log({ res });
      navigate("/login");
      toast.success("Logout successful!");
    } catch (e) {
      console.log({ e });
    } finally {
      setLoading(false);
    }
  };

  // Simulate loading profile for demo
  // Remove in production, use real loading logic
  useState(() => {
    const timer = setTimeout(() => setProfileLoading(false), 1000);
    return () => clearTimeout(timer);
  });

  if (loading) return <Loader />;

  return (
    <header className="bg-white border-b border-b-border md:py-4 md:px-10 py-2 px-3">
      <div className="flex items-center">
        <div className="flex items-center gap-3">
          <button
            className="cursor-pointer"
            onClick={toggleSidebar}
            type="button"
          >
            <Icon name="MenuIcon" />
          </button>
          <div className="w-[120px]">
            <Link to="/">
              <img src="/login-logo.png" alt="Logo" />
            </Link>
          </div>
        </div>
        <div className="flex grow justify-end">
          <div className="flex items-center gap-3 mr-3 md:mr-6">
            <IconButton
              icon={
                <Icon name="BellIcon" color="#fff" height={16} width={16} />
              }
              variant="primary"
            />
            <IconButton
              icon={<Icon name="Mail" color="#EA6A12" height={16} width={16} />}
              variant="light"
            />
            <IconButton
              icon={
                <Icon name="Settings" color="#EA6A12" height={16} width={16} />
              }
              variant="light"
            />
          </div>

          {userInfo ? (
            <div className="flex items-center gap-4 before:content-[''] relative before:absolute before:bg-[#00000033] before:h-[25px] before:w-[1px]">
              <div className="md:flex flex-col md:pl-6 hidden">
                {profileLoading ? (
                  <>
                    <Skeleton
                      height={18}
                      width={120}
                      baseColor="var(--color-orange-light)"
                      highlightColor="var(--color-orange)"
                    />
                    <Skeleton
                      height={14}
                      width={140}
                      baseColor="var(--color-orange-light)"
                      highlightColor="var(--color-orange)"
                    />
                  </>
                ) : (
                  <>
                    <span className="text-base font-semibold text-[#07143B]">
                      Austin Robertson
                    </span>
                    <span className="text-sm font-normal text-[#07143BB2]">
                      Marketing Administrator
                    </span>
                  </>
                )}
              </div>
              <div
                onClick={() => setToggle(!toggle)}
                className={`max-w-9 cursor-pointer md:max-w-11 ml-3 md:ml-0 rounded-full overflow-hidden ${
                  profileLoading ? "" : "border border-border"
                }`}
              >
                {profileLoading ? (
                  <Skeleton
                    circle
                    width={44}
                    height={44}
                    baseColor="var(--color-orange-light)"
                    highlightColor="var(--color-orange)"
                  />
                ) : (
                  <img
                    src="/profile.png"
                    alt="Profile"
                    className="object-cover"
                  />
                )}
              </div>
              {toggle && !profileLoading && (
                <div className="absolute top-12 right-0 bg-white border border-gray-200 rounded shadow-lg z-50 min-w-[120px] text-sm">
                  <button
                    onClick={handleLogout}
                    className="w-full cursor-pointer text-left px-4 py-2 hover:bg-[var(--color-orange-light)] text-[#07143B]"
                  >
                    Logout
                  </button>
                </div>
              )}
            </div>
          ) : (
            <button className="flex gap-1 items-center cursor-pointer justify-center px-2 text-xs py-1 md:px-3 md:py-1.5 md:text-sm rounded-md transition-all duration-300 bg-[var(--color-orange)] text-white hover:opacity-90">
              <Link to={"/login"}>Login</Link>
            </button>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
