import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import type { SubmitHandler } from "react-hook-form";
import axios from "axios";
import toast from "react-hot-toast";
import InputField from "../common/InputField";
import CustomButton from "../common/CustomButton";
import { getApiUrl } from "../../utils/api";

// Interface for category data from API
interface Category {
  id: string;
  name: string;
  description: string;
  outlet: string;
  slug?: string;
}

// Interface for menu form data
interface MenuFormData {
  name: string;
  category: string; // This will store the category slug
  desc: string;
  price: number;
}

const AddMenu: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [categoriesLoading, setCategoriesLoading] = useState(true);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<MenuFormData>();

  // Get token from localStorage
  const token = localStorage.getItem("temp_token");

  // Fetch categories from API
  const getCategories = async () => {
    try {
      setCategoriesLoading(true);
      const res = await axios.get(
        getApiUrl("/restaurant/categories/"),
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      setCategories(res.data);
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to load categories");
    } finally {
      setCategoriesLoading(false);
    }
  };

  console.log(categories,"categories")

  // Handle form submission
  const handleAddMenu: SubmitHandler<MenuFormData> = async (data) => {
    try {
      setLoading(true);

      // Prepare the data for API
      const menuData = {
        name: data.name,
        category: data.category, // category slug
        description: data.desc,
        price: Number(data.price),
      };

      console.log(menuData,"menuData")

      const res = await axios.post(
        getApiUrl("/restaurant/menu-items/"),
        menuData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );

      console.log("Menu item created:", res.data);
      toast.success("Menu item added successfully!");
      reset(); // Reset form after successful submission
    } catch (error) {
      console.error("Error adding menu item:", error);
      toast.error("Failed to add menu item");
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories on component mount
  useEffect(() => {
    if (token) {
      getCategories();
    }
  }, [token]);

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Add New Menu Item</h2>

      <form onSubmit={handleSubmit(handleAddMenu)} className="space-y-6">
        {/* Name Field */}
        <div>
          <InputField
            id="name"
            type="text"
            label="Menu Item Name *"
            placeholder="Enter menu item name"
            {...register("name", {
              required: "Menu item name is required",
              minLength: {
                value: 2,
                message: "Name must be at least 2 characters long",
              },
            })}
            error={errors.name?.message}
          />
        </div>

        {/* Category Dropdown */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium mb-1">
            Category *
          </label>
          <select
            id="category"
            {...register("category", {
              required: "Please select a category",
            })}
            className={`w-full rounded-md border border-[#E6E6E6] px-3 py-2 text-sm outline-none focus:border-[var(--color-orange)] transition-all duration-200 ${
              errors.category ? "border-red-500" : ""
            }`}
            disabled={categoriesLoading}
          >
            <option value="">
              {categoriesLoading ? "Loading categories..." : "Select a category"}
            </option>
            {categories.map((category) => (
              <option key={category.slug} value={category.slug || category.id}>
                {category.name}
              </option>
            ))}
          </select>
          {errors.category && (
            <p className="mt-1 text-xs text-red-500">{errors.category.message}</p>
          )}
        </div>

        {/* Description Field */}
        <div>
          <label htmlFor="desc" className="block text-sm font-medium mb-1">
            Description *
          </label>
          <textarea
            id="desc"
            {...register("desc", {
              required: "Description is required",
              minLength: {
                value: 10,
                message: "Description must be at least 10 characters long",
              },
            })}
            placeholder="Enter menu item description"
            rows={4}
            className={`w-full rounded-md border border-[#E6E6E6] px-3 py-2 text-sm outline-none focus:border-[var(--color-orange)] transition-all duration-200 resize-vertical ${
              errors.desc ? "border-red-500" : ""
            }`}
          />
          {errors.desc && (
            <p className="mt-1 text-xs text-red-500">{errors.desc.message}</p>
          )}
        </div>

        {/* Price Field */}
        <div>
          <InputField
            id="price"
            type="number"
            label="Price *"
            placeholder="Enter price"
            step="0.01"
            min="0"
            {...register("price", {
              required: "Price is required",
              min: {
                value: 0.01,
                message: "Price must be greater than 0",
              },
              valueAsNumber: true,
            })}
            error={errors.price?.message}
          />
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <CustomButton
            label="Cancel"
            onClick={() => reset()}
            bgColor="bg-gray-500"
            textColor="text-white"
            className="px-6 py-2"
          />
          <CustomButton
            label={loading ? "Adding..." : "Add Menu Item"}
            bgColor="bg-[var(--color-orange)]"
            textColor="text-white"
            className="px-6 py-2"
            disabled={loading || categoriesLoading}
          />
        </div>
      </form>
    </div>
  );
};

export default AddMenu;