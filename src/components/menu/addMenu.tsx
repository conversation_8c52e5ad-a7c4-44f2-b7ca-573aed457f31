import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import type { SubmitHandler } from "react-hook-form";
import axios from "axios";
import toast from "react-hot-toast";
import InputField from "../common/InputField";
import CustomButton from "../common/CustomButton";
import { getApiUrl } from "../../utils/api";
import { useApi } from "../../context/ApiContext";
import { useAuth } from "../../context/AuthContext";

// Interface for menu form data
interface MenuFormData {
  name: string;
  category_slug: string; // This will store the category slug
  desc: string;
  price: number;
  image: FileList;
}

const AddMenu: React.FC = () => {
  const { categories } = useApi();
  const { loading, setLoading, outletSlug } = useAuth();
  const [isToggled, setIsToggled] = useState<boolean>(false);
  const [descLoading, setDescLoading] = useState<boolean>(false);
  const [improvedDescription, setImprovedDescription] = useState<string>("");

  const handleToggle = () => {
    setIsToggled(!isToggled);
  };

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors },
    reset,
    control,
  } = useForm<MenuFormData>({
    mode: "onSubmit", // best for this case
  });

  // Handle form submission
  // const handleAddMenu: SubmitHandler<MenuFormData> = async (data) => {
  //   console.log({ data });
  //   const formData = new FormData();

  //   formData.append("name", data.name);
  //   formData.append("category_slug", data.category_slug);
  //   formData.append("description", data.desc); // Note: description, not desc
  //   formData.append("price", data.price.toString());
  //   formData.append("is_available", "true");
  //   if (isToggled) {
  //     formData.append("image", data.image[0]);
  //   }
  //   console.log("FormData contents:");
  //   for (let [key, value] of formData.entries()) {
  //     console.log(key, value);
  //   }

  //   const apiUrl = getApiUrl(
  //     `/api/protected/restaurant/${outletSlug?.slug}/items${isToggled ? "" : "/no-image"}`
  //   );
  //   console.log(apiUrl, "apiUrl");

  //   try {
  //     setLoading(true);
  //     await axios.post(apiUrl, formData, {
  //       withCredentials: true,
  //     });

  //     toast.success("Menu item added successfully!");
  //     reset(); // Reset form after successful submission
  //   } catch (error) {
  //     console.error("Error adding menu item:", error);
  //     toast.error("Failed to add menu item");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const typeAIText = async (text: string, delay = 20) => {
    let current = "";
    for (let i = 0; i < text.length; i++) {
      current += text[i];
      setValue("desc", current);
      await new Promise((r) => setTimeout(r, delay));
    }
  };

  const handleImproveAI = async () => {
    const getDesc = getValues("desc");
    const getName = getValues("name");

    if (!getDesc || !getDesc) {
      toast.error("Please fill out name and description first.");
      return;
    }

    const params = new URLSearchParams({
      item_name: getDesc,
      description: getDesc,
    });

    try {
      setDescLoading(true);
      console.log("called");
      const res = await axios.get(
        getApiUrl(
          `/api/protected/restaurant/${outletSlug?.slug}/items/enhance_description_with_ai?${params}`
        )
      );
      setImprovedDescription(res?.data?.data);
      console.log({ res });

      if (res.status === 200 && res?.data?.data) {
        const improved = res.data.data;
        await typeAIText(improved); // ✅ directly set into textarea
        setImprovedDescription(improved); // optional: store in state if needed elsewhere
        toast.success("Description improved!");
      } else {
        toast.error("Could not improve the description.");
      }
    } catch (e) {
      toast.error("Failed to improve AI");
      console.log(e, "error");
    } finally {
      setDescLoading(false);
    }

    console.log({ getDesc, getName });
  };

  console.log({ improvedDescription });

  const handleAddMenu: SubmitHandler<MenuFormData> = async (data) => {
    console.log({ data });

    const apiUrl = getApiUrl(
      `/api/protected/restaurant/${outletSlug?.slug}/items${isToggled ? "" : "/no-image"}`
    );
    console.log(apiUrl, "apiUrl");

    try {
      setLoading(true);

      if (isToggled) {
        // Send multipart/form-data
        const formData = new FormData();
        formData.append("name", data.name);
        formData.append("category_slug", data.category_slug);
        formData.append(
          "description",
          improvedDescription ? improvedDescription : data.desc
        ); // 'desc' → 'description'
        formData.append("price", data.price.toString());
        formData.append("image", data.image[0]);

        await axios.post(apiUrl, formData, {
          withCredentials: true,
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      } else {
        // Send plain JSON
        const jsonData = {
          name: data.name,
          category_slug: data.category_slug,
          description: improvedDescription ? improvedDescription : data.desc,
          price: data.price,
        };

        await axios.post(apiUrl, jsonData, {
          withCredentials: true,
          headers: {
            "Content-Type": "application/json",
          },
        });
      }

      toast.success("Menu item added successfully!");
      reset(); // Reset form after successful submission
    } catch (error) {
      console.error("Error adding menu item:", error);
      toast.error("Failed to add menu item");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // getCategories();
  }, []);

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl md:text-[34px] font-bold mb-6">Add New Menu Item</h2>

      <form onSubmit={handleSubmit(handleAddMenu)} className="space-y-6">
        {/* Name Field */}
        <div>
          <InputField
            id="name"
            type="text"
            label="Item Name *"
            placeholder="Menu Item Name"
            className="h-[46px] rounded-md border-[#E6E6E6]"
            {...register("name", {
              required: "Menu item name is required",
              minLength: {
                value: 2,
                message: "Name must be at least 2 characters long",
              },
            })}
            error={errors.name?.message}
          />
        </div>

        <div className="flex md:flex-row flex-col items-center gap-[18px] w-full">
          {/* Price Field */}
          <div className="w-full">
            <InputField
              id="price"
              type="number"
              label="Menu Price *"
              placeholder="Enter Menu Price"
              step="0.01"
              min="0"
              className="h-[46px] rounded-md border-[#E6E6E6]"
              {...register("price", {
                required: "Price is required",
                min: {
                  value: 0.01,
                  message: "Price must be greater than 0",
                },
                valueAsNumber: true,
              })}
              error={errors.price?.message}
            />
          </div>

          {/* Category Dropdown */}
          <div className="w-full">
            <label
              htmlFor="category"
              className="block text-sm font-medium mb-3"
            >
              Category *
            </label>
            <select
              id="category"
              {...register("category_slug", {
                required: "Please select a category",
              })}
              className={`w-full border h-[46px] rounded-md border-[#E6E6E6] px-3 py-2 text-sm outline-none focus:border-[var(--color-orange)] transition-all duration-200 ${
                errors.category_slug ? "border-red-500" : ""
              }`}
              disabled={loading}
            >
              <option value="">
                {loading ? "Loading categories..." : "Select a category"}
              </option>
              {categories?.map((category) => (
                <option
                  key={category.slug}
                  value={category.slug || category.id}
                >
                  {category.name}
                </option>
              ))}
            </select>
            {errors.category_slug && (
              <p className="mt-1 text-xs text-red-500">
                {errors.category_slug.message}
              </p>
            )}
          </div>
        </div>

        {/* Description Field */}
        <div className="relative">
          <label htmlFor="desc" className="block text-sm font-medium mb-1">
            Description *
          </label>

          <Controller
            name="desc"
            control={control}
            rules={{
              required: "Description is required",
              minLength: {
                value: 10,
                message: "Description must be at least 10 characters long",
              },
            }}
            render={({ field }) => (
              <textarea
                {...field}
                id="desc"
                rows={8}
                disabled={descLoading}
                placeholder={
                  descLoading
                    ? "Improving description..."
                    : "Enter Menu Item Description"
                }
                className={`w-full rounded-md border border-[#E6E6E6] px-3 py-2 pr-28 text-sm outline-none transition-all duration-200 resize-vertical ${
                  errors.desc
                    ? "border-red-500"
                    : "focus:border-[var(--color-orange)]"
                } ${descLoading ? "bg-gray-100 text-gray-500 cursor-wait" : ""}`}
              />
            )}
          />

          <button
            type="button"
            onClick={handleImproveAI}
            disabled={descLoading}
            className={`absolute bottom-4 flex items-center gap-2 cursor-pointer right-2 text-xs p-3 rounded-md shadow transition ${
              descLoading
                ? "bg-[#7C3AED] text-white cursor-wait"
                : "bg-[#8B5CF6] text-white"
            }`}
          >
            <span>
              <svg
                width="16"
                height="17"
                viewBox="0 0 16 17"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_2677_73)">
                  <path
                    d="M4.95787 3.62842L5.89831 6.04133C5.98512 6.26406 6.0285 6.37542 6.09498 6.46934C6.15388 6.55257 6.22591 6.62545 6.30819 6.68507C6.40099 6.75232 6.51104 6.79625 6.73117 6.88409L9.11565 7.83573L6.73117 8.78737C6.51104 8.8752 6.40099 8.91908 6.30819 8.98635C6.22591 9.04601 6.15388 9.11887 6.09498 9.20209C6.0285 9.29607 5.98512 9.40738 5.89831 9.63016L4.95787 12.043L4.01743 9.63016C3.93061 9.40738 3.8872 9.29607 3.82074 9.20209C3.76183 9.11887 3.6898 9.04601 3.60754 8.98635C3.51474 8.91908 3.40468 8.8752 3.18457 8.78737L0.800049 7.83573L3.18457 6.88409C3.40468 6.79625 3.51474 6.75232 3.60754 6.68507C3.6898 6.62545 3.76183 6.55257 3.82074 6.46934C3.8872 6.37542 3.93061 6.26406 4.01743 6.04133L4.95787 3.62842Z"
                    fill="white"
                    stroke="white"
                    stroke-width="1.15495"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M12.2618 11.2017L12.8512 12.7138C12.9056 12.8533 12.9327 12.9231 12.9744 12.982C13.0113 13.0341 13.0565 13.0798 13.108 13.1172C13.1662 13.1593 13.2351 13.1869 13.3731 13.2419L14.8674 13.8383L13.3731 14.4346C13.2351 14.4896 13.1662 14.5171 13.108 14.5593C13.0565 14.5966 13.0113 14.6424 12.9744 14.6945C12.9327 14.7534 12.9056 14.8231 12.8512 14.9627L12.2618 16.4748L11.6725 14.9627C11.6181 14.8231 11.5909 14.7534 11.5492 14.6945C11.5123 14.6424 11.4672 14.5966 11.4156 14.5593C11.3574 14.5171 11.2885 14.4896 11.1506 14.4346L9.65625 13.8383L11.1506 13.2419C11.2885 13.1869 11.3574 13.1593 11.4156 13.1172C11.4672 13.0798 11.5123 13.0341 11.5492 12.982C11.5909 12.9231 11.6181 12.8533 11.6725 12.7138L12.2618 11.2017Z"
                    fill="white"
                    stroke="white"
                    stroke-width="0.635279"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M12.5946 0.80957L13.184 2.32166C13.2384 2.46124 13.2655 2.53103 13.3072 2.58987C13.3441 2.64204 13.3893 2.68771 13.4408 2.72507C13.4989 2.76722 13.5679 2.79474 13.7059 2.8498L15.2001 3.44615L13.7059 4.04252C13.5679 4.09756 13.4989 4.12507 13.4408 4.16723C13.3893 4.20458 13.3441 4.25025 13.3072 4.30243C13.2655 4.36128 13.2384 4.43106 13.184 4.57066L12.5946 6.08274L12.0053 4.57066C11.9509 4.43106 11.9237 4.36128 11.882 4.30243C11.8451 4.25025 11.8 4.20458 11.7484 4.16723C11.6902 4.12507 11.6213 4.09756 11.4833 4.04252L9.98901 3.44615L11.4833 2.8498C11.6213 2.79474 11.6902 2.76722 11.7484 2.72507C11.8 2.68771 11.8451 2.64204 11.882 2.58987C11.9237 2.53103 11.9509 2.46124 12.0053 2.32166L12.5946 0.80957Z"
                    fill="white"
                    stroke="white"
                    stroke-width="0.635279"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_2677_73">
                    <rect width="16" height="17" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </span>
            <span>{descLoading ? "Improving..." : "Improve with AI"}</span>
          </button>

          {errors.desc && (
            <p className="mt-1 text-xs text-red-500">{errors.desc.message}</p>
          )}
        </div>

        <div className="flex items-center justify-between p-[18px] rounded-[10px] bg-[#F3F4F6] border border-[#E6E6E6]">
          <div className="mb-2">
            <label className="block font-semibold">
              {isToggled ? "Upload Custom Image" : "AI-Generated Image"}
            </label>
            <p className="text-sm font-medium text-gray-500 mt-1">
              {isToggled
                ? "Upload your own image file (JPEG, PNG max 5MB)"
                : "An image will be automatically generated using AI"}
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <button
              type="button"
              onClick={handleToggle}
              className={`
              relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ease-in-out
              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500
              ${isToggled ? "bg-[#ea6a12]" : "bg-gray-200"}
            `}
              style={{
                backgroundColor: isToggled ? "#ea6a12" : "#e5e7eb",
              }}
            >
              <span
                className={`
                inline-block w-5 h-5 bg-white rounded-full shadow-lg transform transition-transform duration-200 ease-in-out
                ${isToggled ? "translate-x-5" : "translate-x-0.5"}
              `}
              />
            </button>
          </div>
        </div>

        {isToggled && (
          <div>
            <InputField
              id="image"
              // label="Menu Item Image *"
              type="file"
              accept="image/*"
              {...register("image", {
                required: "Image is required",
                validate: {
                  fileType: (files: FileList) => {
                    if (!files || files.length === 0) return true;
                    const file = files[0];
                    const allowedTypes = [
                      "image/jpeg",
                      "image/jpg",
                      "image/png",
                      "image/webp",
                      "image/gif",
                    ];
                    return (
                      allowedTypes.includes(file.type) ||
                      "Please select a valid image file (JPEG, PNG, WebP, GIF)"
                    );
                  },
                  fileSize: (files: FileList) => {
                    if (!files || files.length === 0) return true;
                    const file = files[0];
                    const maxSize = 5 * 1024 * 1024; // 5MB
                    return (
                      file.size <= maxSize || "File size must be less than 5MB"
                    );
                  },
                },
              })}
              error={errors.image?.message}
            />
          </div>
        )}

        {/* Submit Button */}
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row justify-end space-x-4">
          <CustomButton
            label="Cancel Menu Item"
            onClick={() => reset()}
            bgColor="bg-white"
            textColor="text-#4B5563"
            className="px-6 border-[#D1D5DB] border md:max-w-[230px] w-full !py-[18px]" 
          />
          <CustomButton
            label={loading ? "Adding..." : "Add Menu Item"}
            bgColor="bg-[var(--color-orange)]"
            textColor="text-white"
            className="px-6 border-[var(--color-orange)] border md:max-w-[230px] w-full !py-[18px]" 
            disabled={loading || loading}
          />
        </div>
      </form>
    </div>
  );
};

export default AddMenu;
