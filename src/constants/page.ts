import type { Page } from "../types/common";

export const PAGE_DASHBOARD: Page = {
  id: "DASHBOARD",
  path: "/",
  name: "Dashboard",
};

export const PAGE_CATEGORIES: Page = {
  id: "CATEGORIES",
  path: "/categories",
  name: "CATEGORIES",
};

export const PAGE_CATEGORIES_LIST: Page = {
  id: "CATEGORIES_LIST",
  path: "/categories/list",
  name: "CATEGORIES_LIST",
};

export const PAGE_CATEGORIES_ADD: Page = {
  id: "CATEGORIES_ADD",
  path: "/categories/add",
  name: "CATEGORIES_ADD",
};

export const PAGE_ORDER: Page = {
  id: "ORDER",
  path: "/order",
  name: "ORDER",
};

export const PAGE_MENUS: Page = {
  id: "MENUS",
  path: "/menus",
  name: "MENUS",
};

export const PAGE_LOGIN: Page = {
  id: "LOGI<PERSON>",
  path: "/login",
  name: "LOGIN",
};
