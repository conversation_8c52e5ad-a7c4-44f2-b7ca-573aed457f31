import React, { useState } from "react";
import { Route, Routes } from "react-router-dom";
import {
  PAGE_CATEGORIES_ADD,
  PAGE_CATEGORIES_LIST,
  PAGE_DASHBOARD,
  PAGE_LOGIN,
  PAGE_MENU_ADD,
  PAGE_MENU_LIST,
  PAGE_MENUS,
  PAGE_ORDER,
} from "../constants/page";
import DashboardPage from "../pages/dashboard/dashboardPage";
import OrderPage from "../pages/order/orderPage";
import { MenusPage, MenuListPage } from "../pages/menus";
import { Login } from "../pages/login";
import { Header } from "../components/header";
import { SideBar } from "../components/sideBar";
import CategoriesListPage from "../components/categories/categoriesListPage";
import CategoriesForm from "../components/categories/categoriesForm";
import AddMenu from "../components/menu/addMenu";

export const RoutesProvider: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);

  const toggleSidebar = (): void => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex">
      <SideBar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
      <div
        className={`grow bg-[#F6F6F6] min-h-[100vh] transition-all duration-300 ease-in-out`}
      >
        <Header toggleSidebar={toggleSidebar} isSidebarOpen={isSidebarOpen} />
        <main className="py-[10px] px-3 lg:p-10">
          <Routes>
            <Route path={PAGE_MENU_LIST.path} element={<MenuListPage />} />
            <Route path={PAGE_DASHBOARD.path} element={<DashboardPage />} />
            <Route path={PAGE_ORDER.path} element={<OrderPage />} />
            <Route path={PAGE_MENUS.path} element={<MenusPage />} />
            <Route path={PAGE_LOGIN.path} element={<Login />} />
            <Route
              path={PAGE_MENU_ADD.path}
              element={<AddMenu />}
            />
            <Route
              path={PAGE_CATEGORIES_LIST.path}
              element={<CategoriesListPage />}
            />
            <Route
              path={PAGE_CATEGORIES_ADD.path}
              element={<CategoriesForm />}
            />
          </Routes>
        </main>
      </div>
    </div>
  );
};
