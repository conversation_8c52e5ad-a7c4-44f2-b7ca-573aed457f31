import { defineConfig } from "vite";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  optimizeDeps: {
    // lucide-react is now efficiently bundled through our custom Icon component
  },
  server: {
    proxy: {
      "/api": {
        // target: "https://test.dishto.in",
        target: "http://192.168.112.106:8001",
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api/, ""), // remove /api
      },
    },
  },
});
